import { RouteRecordRaw } from 'vue-router';
import Layout from '@/layouts/index.vue';

const testRoutes: Array<RouteRecordRaw> = [
  {
    path: '/test',
    component: Layout,
    redirect: '/test/script-intellisense',
    name: 'test',
    meta: {
      title: '测试页面',
      icon: 'tools',
    },
    children: [
      {
        path: 'script-intellisense',
        name: 'ScriptIntelliSense',
        component: () => import('@/pages/test/script-intellisense.vue'),
        meta: {
          title: 'Script智能提示测试',
        },
      },
    ],
  },
];

export default testRoutes;
