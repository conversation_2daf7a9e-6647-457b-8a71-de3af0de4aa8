<template>
  <div class="script-intellisense-test">
    <h1>Script智能提示测试页面</h1>

    <div class="test-section">
      <h2>1. ValueDialog 测试</h2>
      <p>点击按钮打开ValueDialog，选择"动态脚本"类型测试智能提示功能</p>
      <t-button @click="showValueDialog = true">打开 ValueDialog</t-button>
      <div v-if="selectedValue" class="result">
        <h3>选择的值：</h3>
        <pre>{{ JSON.stringify(selectedValue, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h2>2. ScriptEditorPanel 测试</h2>
      <p>在下面的编辑器中测试智能提示功能：</p>
      <ul>
        <li>输入 <code>Utils.</code> 查看函数提示</li>
        <li>输入 <code>_data.</code> 查看变量提示</li>
        <li>输入临时变量名查看提示</li>
      </ul>
      <script-editor-panel v-model:script="testScript" />
      <div class="result">
        <h3>当前脚本内容：</h3>
        <pre>{{ testScript }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h2>3. 直接Editor测试</h2>
      <p>直接使用Editor组件测试智能提示：</p>
      <editor
        v-model:value="directScript"
        language="typescript"
        :enable-intellisense="true"
        :current-variables="mockCurrentVariables"
        :local-variables="mockLocalVariables"
        :global-variables="mockGlobalVariables"
        :functions="mockFunctions"
        style="height: 200px; margin: 16px 0"
      />
      <div class="result">
        <h3>直接编辑器内容：</h3>
        <pre>{{ directScript }}</pre>
      </div>
    </div>

    <!-- ValueDialog 组件 -->
    <value-dialog />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useActionFlowStore } from '@/components/action-panel/store/index';
import ValueDialog from '@/components/action-panel/ValueDialog.vue';
import ScriptEditorPanel from '@/components/action-panel/ScriptEditorPanel.vue';
import Editor from '@/components/editor/index.vue';

const actionFlowStore = useActionFlowStore();

// 测试数据
const showValueDialog = ref(false);
const selectedValue = ref(null);
const testScript = ref('// 在这里测试智能提示\n// 输入 Utils. 或 _data. 查看提示\n');
const directScript = ref('// 直接编辑器测试\n// 输入变量名或函数名查看提示\n');

// 模拟变量数据
const mockCurrentVariables = ref([
  {
    id: '1',
    key: 'userName',
    path: 'userName',
    pathDescription: '用户名',
    description: '当前用户名',
    type: 'string',
  },
  {
    id: '2',
    key: 'userAge',
    path: 'userAge',
    pathDescription: '用户年龄',
    description: '当前用户年龄',
    type: 'number',
  },
  {
    id: '3',
    key: 'userInfo',
    path: 'userInfo',
    pathDescription: '用户信息',
    description: '用户详细信息',
    type: 'object',
    children: [
      {
        id: '3-1',
        key: 'email',
        path: 'userInfo.email',
        pathDescription: '邮箱',
        description: '用户邮箱地址',
        type: 'string',
      },
      {
        id: '3-2',
        key: 'phone',
        path: 'userInfo.phone',
        pathDescription: '电话',
        description: '用户电话号码',
        type: 'string',
      },
    ],
  },
]);

const mockLocalVariables = ref([
  {
    id: '4',
    key: 'localVar1',
    path: 'localVar1',
    pathDescription: '局部变量1',
    description: '测试局部变量',
    type: 'string',
  },
  {
    id: '5',
    key: 'localVar2',
    path: 'localVar2',
    pathDescription: '局部变量2',
    description: '另一个局部变量',
    type: 'number',
  },
]);

const mockGlobalVariables = ref([
  {
    id: '6',
    key: 'globalConfig',
    path: 'globalConfig',
    pathDescription: '全局配置',
    description: '系统全局配置',
    type: 'object',
    children: [
      {
        id: '6-1',
        key: 'apiUrl',
        path: 'globalConfig.apiUrl',
        pathDescription: 'API地址',
        description: '系统API地址',
        type: 'string',
      },
    ],
  },
]);

const mockFunctions = ref([
  {
    value: 'UUID',
    label: '随机ID',
    script: 'Utils.UUID()',
    remark: '生成随机的UUID',
  },
  {
    value: 'NOW',
    label: '当前时间',
    script: 'Utils.NOW()',
    remark: '获取当前时间',
  },
  {
    value: 'STRING_CONCAT',
    label: '字符串拼接',
    script: 'Utils.STRING_CONCAT(str1, str2)',
    remark: '拼接多个字符串',
  },
]);

// 设置模拟数据到store
actionFlowStore.currentVariables = mockCurrentVariables.value;
actionFlowStore.localVariables = mockLocalVariables.value;

// 监听ValueDialog的值变化
const { currentValueInputData, isSaveValue } = storeToRefs(actionFlowStore);
watch(
  () => isSaveValue.value,
  (newVal) => {
    if (newVal && currentValueInputData.value?.value) {
      selectedValue.value = currentValueInputData.value.value;
    }
  },
);
</script>

<style lang="less" scoped>
.script-intellisense-test {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;

  h1 {
    color: #1f2937;
    margin-bottom: 32px;
  }

  .test-section {
    margin-bottom: 48px;
    padding: 24px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background: #f9fafb;

    h2 {
      color: #374151;
      margin-bottom: 16px;
    }

    p {
      color: #6b7280;
      margin-bottom: 16px;
    }

    ul {
      color: #6b7280;
      margin-bottom: 16px;
      padding-left: 20px;

      li {
        margin-bottom: 8px;
      }

      code {
        background: #f3f4f6;
        padding: 2px 6px;
        border-radius: 4px;
        font-family: 'Courier New', monospace;
      }
    }

    .result {
      margin-top: 16px;
      padding: 16px;
      background: white;
      border-radius: 6px;
      border: 1px solid #d1d5db;

      h3 {
        color: #374151;
        margin-bottom: 12px;
        font-size: 14px;
      }

      pre {
        background: #f8f9fa;
        padding: 12px;
        border-radius: 4px;
        font-size: 12px;
        overflow-x: auto;
        margin: 0;
      }
    }
  }
}
</style>
