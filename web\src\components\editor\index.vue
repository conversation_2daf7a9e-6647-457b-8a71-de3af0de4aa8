<template>
  <vue-monaco-editor
    class="editor"
    :language="props.language"
    theme="vs-dark"
    :options="MONACO_EDITOR_OPTIONS"
    @mount="handleMount"
  />
</template>
<script lang="ts">
export default {
  name: 'Editor',
};
</script>
<script setup lang="ts">
import { VueMonacoEditor } from '@guolao/vue-monaco-editor';
import { editor } from 'monaco-editor';
import * as monaco from 'monaco-editor/esm/vs/editor/editor.api';
import { computed, shallowRef, watch } from 'vue';
import { updateIntelliSenseData } from './scriptCompletion';

const props = withDefaults(
  defineProps<{
    code?: string;
    language: string;
    readOnly?: boolean;
    autoWrap?: boolean;
    showLineNumbers?: boolean;
    // 智能提示相关属性
    enableIntelliSense?: boolean;
    currentVariables?: any[];
    localVariables?: any[];
    globalVariables?: any[];
    functions?: any[];
  }>(),
  {
    language: 'javascript',
    readOnly: false,
    autoWrap: true,
    showLineNumbers: true,
    enableIntelliSense: true,
  },
);

const MONACO_EDITOR_OPTIONS = computed<editor.IStandaloneEditorConstructionOptions>(() => {
  const options: editor.IStandaloneEditorConstructionOptions = {
    automaticLayout: true,
    readOnly: props.readOnly,
    formatOnType: true,
    formatOnPaste: true,
    lineNumbers: props.showLineNumbers ? 'on' : 'off',
    // 启用智能提示相关选项
    quickSuggestions: props.enableIntelliSense,
    suggestOnTriggerCharacters: props.enableIntelliSense,
    acceptSuggestionOnEnter: 'on',
    tabCompletion: 'on',
    wordBasedSuggestions: 'off', // 关闭基于单词的建议，使用我们自定义的
  };
  if (props.autoWrap) {
    options.wordWrap = 'on';
    options.minimap = {
      enabled: false,
    };
  }
  return options;
});

const editorRef = shallowRef<Partial<monaco.editor.IStandaloneCodeEditor>>();
const handleMount = (editor: any) => {
  editorRef.value = editor;
  
  // 如果启用智能提示且是script语言，更新智能提示数据
  if (props.enableIntelliSense && (props.language === 'typescript' || props.language === 'javascript')) {
    updateIntelliSenseData({
      currentVariables: props.currentVariables || [],
      localVariables: props.localVariables || [],
      globalVariables: props.globalVariables || [],
      functions: props.functions || [],
    });
  }
};

// 监听智能提示数据变化
watch(
  () => [props.currentVariables, props.localVariables, props.globalVariables, props.functions],
  () => {
    if (props.enableIntelliSense && (props.language === 'typescript' || props.language === 'javascript')) {
      updateIntelliSenseData({
        currentVariables: props.currentVariables || [],
        localVariables: props.localVariables || [],
        globalVariables: props.globalVariables || [],
        functions: props.functions || [],
      });
    }
  },
  { deep: true }
);

const formatCode = () => {
  editorRef.value?.getAction('editor.action.formatDocument').run();
};

const insertText = (text) => {
  const selection = editorRef.value.getSelection();
  const range = new monaco.Range(
    selection.startLineNumber,
    selection.startColumn,
    selection.endLineNumber,
    selection.endColumn,
  );

  editorRef.value.executeEdits('insertText', [
    {
      range,
      text,
      forceMoveMarkers: true,
    },
  ]);

  editorRef.value.focus();
};

defineExpose({
  formatCode,
  insertText,
  editorRef,
});
</script>
<style lang="less" scoped>
.editor {
  border: 1px solid var(--td-border-level-2-color);
  border-radius: var(--td-radius-default);
}
</style>
