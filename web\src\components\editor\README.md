# Script类型Editor组件智能提示功能

## 功能概述

为script类型的editor组件添加了智能提示支持，包括：
- 临时变量、局部变量、全局变量的智能提示
- 函数的智能提示
- TypeScript类型支持
- 悬停提示显示详细信息

## 文件结构

```
src/components/editor/
├── index.vue              # 主编辑器组件（已更新支持智能提示）
├── loader.ts              # Monaco Editor加载器（已添加scriptCompletion导入）
├── scriptCompletion.ts    # 新增：Script智能提示提供者
├── sqlCompletion.ts       # 原有：SQL智能提示提供者
└── README.md              # 本文档
```

## 核心功能

### 1. scriptCompletion.ts
- 注册TypeScript/JavaScript的completion provider
- 支持变量路径智能提示（临时变量、局部变量、全局变量）
- 支持函数智能提示
- 支持悬停提示显示变量和函数详细信息

### 2. 更新的Editor组件
新增props：
- `enableIntelliSense?: boolean` - 是否启用智能提示（默认true）
- `currentVariables?: any[]` - 临时变量列表
- `localVariables?: any[]` - 局部变量列表  
- `globalVariables?: any[]` - 全局变量列表
- `functions?: any[]` - 函数列表

### 3. 智能提示规则
- 输入临时变量名：直接提示变量路径
- 输入 `_data.`：提示局部变量和全局变量
- 输入 `Utils.`：提示可用函数
- 支持嵌套对象属性提示

## 使用方法

### 在ValueDialog中使用（已集成）
```vue
<editor
  v-show="data.type === 'script'"
  ref="editorRef"
  v-model:value="data.scriptValue"
  :language="codeLanguage"
  :enable-intellisense="true"
  :current-variables="actionFlowStore.currentVariables"
  :local-variables="actionFlowStore.localVariables"
  :global-variables="globalVariables"
  :functions="functionList"
  style="height: 250px"
></editor>
```

### 在ScriptEditorPanel中使用（已集成）
```vue
<editor 
  ref="editorRef" 
  v-model:value="scriptValue" 
  language="typescript" 
  :enable-intellisense="true"
  :current-variables="actionFlowStore.currentVariables"
  :local-variables="actionFlowStore.localVariables"
  :global-variables="globalVariables"
  :functions="functionList"
  style="height: 250px"
></editor>
```

### 在其他组件中使用
```vue
<template>
  <editor
    v-model:value="scriptCode"
    language="typescript"
    :enable-intellisense="true"
    :current-variables="currentVars"
    :local-variables="localVars"
    :global-variables="globalVars"
    :functions="availableFunctions"
  />
</template>

<script setup>
import Editor from '@/components/editor/index.vue';

const currentVars = ref([
  {
    id: '1',
    key: 'userName',
    path: 'userName',
    pathDescription: '用户名',
    description: '当前用户名',
    type: 'string',
  }
]);

const availableFunctions = ref([
  {
    value: 'UUID',
    label: '随机ID',
    script: 'Utils.UUID()',
    remark: '生成随机的UUID',
  }
]);
</script>
```

## 数据格式

### 变量数据格式
```typescript
interface VariableData {
  id: string;
  key: string;
  path?: string;
  pathDescription?: string;
  description?: string;
  type: string;
  children?: VariableData[];
}
```

### 函数数据格式
```typescript
interface FunctionData {
  value: string;
  label: string;
  script: string;
  remark: string;
}
```

## 测试

访问测试页面：`/test/script-intellisense`

测试功能：
1. ValueDialog中的script类型编辑器
2. ScriptEditorPanel组件
3. 直接使用Editor组件

## 智能提示效果

1. **变量提示**：
   - 输入变量名显示类型和描述
   - 支持嵌套对象属性
   - 区分临时变量（无前缀）和数据变量（_data.前缀）

2. **函数提示**：
   - 显示函数名称和用法
   - 显示函数描述
   - 插入完整的函数调用代码

3. **悬停提示**：
   - 鼠标悬停显示变量类型和描述
   - 鼠标悬停显示函数用法和说明

## 注意事项

1. 智能提示仅在language为'typescript'或'javascript'时生效
2. 需要传入正确格式的变量和函数数据
3. 变量数据支持嵌套结构，会递归生成路径提示
4. 函数数据会自动提取并格式化为智能提示格式
